<?php

class ModelExtensionModuleMultiFeedSyncerConnectorsMostcomputers extends Model {
    private $connector_key = 'mostcomputers'; // Ключ на конектора (латиница)
    private $connector_name = 'Most Computers'; // Име на доставчика
    // URL за извличане на XML данни от Most Computers
    private $xml_url = 'https://most.traveldatabank.biz/ProductXML';

    // Кеш за атрибути от XML
    private $attributes_cache = [];
    private $attributes_cache_loaded = false;

    // Кеш за OpenCart атрибути
    private $opencart_attributes_cache = [];
    private $opencart_attributes_cache_loaded = false;

    // Кеш за марки/производители
    private $manufacturers_cache = [];
    private $manufacturers_cache_loaded = false;

    // Система за проследяване на синхронизирани продукти (статични за споделяне между instances)
    private static $current_sync_product_ids = [];
    private static $product_tracking_enabled = false;
    private static $active_instance = null; // Референция към активната instance

    private $logging_enabled = false;
    
    /**
     * Конструктор на класа - задава лимит на паметта
     */
    public function __construct($registry) {
        parent::__construct($registry);
        ini_set('memory_limit', '256M');
    }

    /**
     * Връща данни, необходими за регистриране на този конектор
     * в таблицата `multi_feed_syncer_connectors`.
     * @return array
     */
    public function getInstallData() {
        return [
            'connector'     => $this->connector_name,
            'connector_key' => $this->connector_key,
            'markup_percentage' => 0
        ];
    }

    /**
     * Връща типа на връзката (напр. "XML Link", "API").
     * @return string
     */
    public function getConnectionType() {
        $text_copy_link = 'Копирай';
        $text_copied_link = 'Копирано!';
        $text_copy_error = 'Грешка!';

        $html = 'XML Линк: <span class="text-primary" data-toggle="tooltip" data-placement="top" title="' . htmlspecialchars($this->xml_url) . '">Виж линк</span>';
        $html .= ' <div class="btn-group">';
        $html .= '<button type="button" class="btn btn-default btn-xs btn-copy" data-clipboard-text="' . htmlspecialchars($this->xml_url) . '" title="Копирай XML линка">';
        $html .= '<i class="fa fa-copy"></i> <span class="copy-text">' . $text_copy_link . '</span>';
        $html .= '</button>';
        $html .= '</div>';
        $html .= '<script>';
        $html .= '    $(document).ready(function() {' . "\n";
        $html .= '        $(".btn-copy").off("click.customCopy").on("click.customCopy", function(e) {' . "\n";
        $html .= '            e.preventDefault();' . "\n";
        $html .= '            var $button = $(this);' . "\n";
        $html .= '            var linkText = $button.data("clipboard-text");' . "\n";
        $html .= '            var $copyTextSpan = $button.find(".copy-text");' . "\n";
        $html .= '            var originalText = $copyTextSpan.text();' . "\n";
        $html .= '            var $icon = $button.find("i");' . "\n";
        $html .= '            var originalIconClass = $icon.attr("class");' . "\n";
        $html .= "\n";
        $html .= '            if (linkText && linkText.trim() !== "") {' . "\n";
        $html .= '                navigator.clipboard.writeText(linkText).then(function() {' . "\n";
        $html .= '                    $button.addClass("btn-success");' . "\n";
        $html .= '                    $copyTextSpan.text("' . $text_copied_link . '");' . "\n";
        $html .= '                    $icon.attr("class", "fa fa-check");' . "\n";
        $html .= "\n";
        $html .= '                    var $copiedMsg = $("<span>Копирано</span>").css({' . "\n";
        $html .= '                        "position": "fixed", "top": "20px", "left": "50%", "transform": "translateX(-50%)",' . "\n";
        $html .= '                        "background-color": "#4CAF50", "color": "white", "padding": "10px 20px",' . "\n";
        $html .= '                        "border-radius": "5px", "z-index": "10000", "font-size": "16px", "display": "none"' . "\n";
        $html .= '                    });' . "\n";
        $html .= '                    $("body").append($copiedMsg);' . "\n";
        $html .= '                    $copiedMsg.fadeIn(200).delay(1500).fadeOut(500, function() { $(this).remove(); });' . "\n";
        $html .= "\n";
        $html .= '                    setTimeout(function() {' . "\n";
        $html .= '                        $button.removeClass("btn-success");' . "\n";
        $html .= '                        $copyTextSpan.text(originalText);' . "\n";
        $html .= '                        $icon.attr("class", originalIconClass);' . "\n";
        $html .= '                    }, 2000);' . "\n";
        $html .= '                }).catch(function(err) {' . "\n";
        $html .= '                    console.error("Грешка при копиране: ", err);' . "\n";
        $html .= '                    $copyTextSpan.text("' . $text_copy_error . '");' . "\n";
        $html .= '                    $icon.attr("class", "fa fa-times");' . "\n";
        $html .= "\n";
        $html .= '                    var $errorMsg = $("<span>Грешка при копиране</span>").css({' . "\n";
        $html .= '                        "position": "fixed", "top": "20px", "left": "50%", "transform": "translateX(-50%)",';
        $html .= '                        "background-color": "#f44336", "color": "white", "padding": "10px 20px",';
        $html .= '                        "border-radius": "5px", "z-index": "10000", "font-size": "16px", "display": "none"';
        $html .= '                    });' . "\n";
        $html .= '                    $("body").append($errorMsg);' . "\n";
        $html .= '                    $errorMsg.fadeIn(200).delay(2000).fadeOut(500, function() { $(this).remove(); });' . "\n";
        $html .= "\n";
        $html .= '                    setTimeout(function() {' . "\n";
        $html .= '                        $copyTextSpan.text(originalText);' . "\n";
        $html .= '                        $icon.attr("class", originalIconClass);' . "\n";
        $html .= '                    }, 2500);' . "\n";
        $html .= '                });' . "\n";
        $html .= '            } else {' . "\n";
        $html .= '                console.warn("Няма текст за копиране в data-clipboard-text атрибута на бутона.");' . "\n";
        $html .= '            }' . "\n";
        $html .= '        });' . "\n";
        $html .= '    });' . "\n";
        $html .= '</script>';
        $html .= '<style>.btn-copy { position: relative; transition: all 0.2s ease-in-out; } .btn-copy.btn-success { background-color: #5cb85c !important; border-color: #4cae4c !important; color: #fff !important; } .btn-copy.btn-success .fa-copy { display:none; } .btn-copy .fa-check { display:none; } .btn-copy.btn-success .fa-check { display:inline-block; }</style>';
        return $html;
    }

    /**
     * Връща информация за настройка на cron задача.
     * @return string
     */
    public function getCronInfo() {
        $cron_file_path = DIR_STORAGE . 'cron/multi_feed_syncer.php';
        $command = '/usr/local/bin/php ' . $cron_file_path . ' mostcomputers >> ' . DIR_LOGS . 'multi_feed_syncer.log 2>&1';
        return $command;
    }

    /**
     * Проверява дали източникът на данни (напр. XML линк) е достъпен.
     * @return bool True ако е достъпен, false в противен случай.
     */
    public function checkConnection() {
        $ch = curl_init($this->xml_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return ($http_code >= 200 && $http_code < 300);
    }

    /**
     * Извлича данни от доставчика.
     * @return string Сурови данни (напр. XML низ).
     */
    public function requestSyncData() {
        $ch = curl_init($this->xml_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300);
        $xml_data = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code == 200 && $xml_data) {
            return $xml_data;
        }
        return false;
    }

    /**
     * Конвертира XML данни в PHP масив.
     * @param string $xml_data
     * @return array
     */
    public function convertXMLdataToArray($xml_data) {
        $xml = simplexml_load_string($xml_data, "SimpleXMLElement", LIBXML_NOCDATA | LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

        if ($xml === false) {
            return [];
        }

        $json = json_encode($xml, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $array = json_decode($json, TRUE);

        return $array ? $array : [];
    }

    /**
     * Мапира масива с данни от доставчика към структурата на Opencart продукт.
     * @param array $supplier_product_data Данните за един продукт от доставчика
     * @param array $full_feed_data Пълният фийд данни (за достъп до глобални елементи)
     * @return array Масив с данни за Opencart продукт.
     */
    public function mapDataToOpencartProduct(array $supplier_product_data, array $full_feed_data = []) {
        $opencart_product = $this->_initializeOpencartProduct();

        $this->_mapBasicProductData($opencart_product, $supplier_product_data, $full_feed_data);
        $this->_mapProductImages($opencart_product, $supplier_product_data);
        $this->_mapProductWeight($opencart_product, $supplier_product_data);
        $this->_mapProductStockStatus($opencart_product, $supplier_product_data);
        $this->_mapProductCategories($opencart_product, $supplier_product_data);
        $this->_mapProductManufacturer($opencart_product, $supplier_product_data);
        $this->_mapProductAttributes($opencart_product, $supplier_product_data);

        $language_id = (int)$this->config->get('config_language_id') ?: 1;
        $processed_attributes = $this->processProductAttributes($opencart_product, $language_id);

        // Заменяме attributes_data_source с обработените атрибути
        $opencart_product['processed_attributes'] = $processed_attributes;

        // Обработваме марката на продукта от атрибутите
        $manufacturer_id = $this->processProductManufacturer($opencart_product);
        if ($manufacturer_id) {
            $opencart_product['manufacturer_id'] = $manufacturer_id;
            if(!$opencart_product['manufacturer'] && $opencart_product['manufacturer_name_source']) {
                $opencart_product['manufacturer'] = $opencart_product['manufacturer_name_source'];
            }
        }

        // Освобождаваме входните данни за спестяване на памет
        unset($supplier_product_data, $full_feed_data);

        return $opencart_product;
    }

    /**
     * Извлича текстова стойност от потенциално вложен масив
     * @param mixed $node_data Данните от XML възела.
     * @param string $default Стойност по подразбиране.
     * @return string Текстовата стойност или стойността по подразбиране.
     */
    private function _getXmlNodeValue($node_data, $default = '') {
        if (is_array($node_data)) {
            if (isset($node_data['#text'])) {
                $value = (string)$node_data['#text'];
                return html_entity_decode($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            }
            if (isset($node_data[0]) && is_string($node_data[0])) {
                $value = (string)$node_data[0];
                return html_entity_decode($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            }
            return $default;
        }

        $value = (string)$node_data;
        return html_entity_decode($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }

    private function _initializeOpencartProduct() {
        $opencart_product = [];
        $oc_fields = [
            'product_id', 'name', 'description', 'meta_title', 'meta_description',
            'meta_keyword', 'tag', 'model', 'sku', 'upc', 'ean', 'jan', 'isbn', 'mpn',
            'location', 'quantity', 'stock_status_id', 'image', 'manufacturer_id',
            'manufacturer', 'price', 'special', 'reward', 'points', 'tax_class_id',
            'date_available', 'weight', 'weight_class_id', 'length', 'width', 'height',
            'length_class_id', 'subtract', 'minimum', 'sort_order', 'status',
            'date_added', 'date_modified', 'viewed',
            'product_category', 'product_attribute', 'product_option', 'product_image',
            'categories_data_source', 'attributes_data_source', 'manufacturer_name_source'
        ];

        foreach ($oc_fields as $field) {
            $opencart_product[$field] = '';
            if (in_array($field, ['product_category', 'product_attribute', 'product_option', 'product_image', 'categories_data_source', 'attributes_data_source'])) {
                $opencart_product[$field] = [];
            }
        }
        return $opencart_product;
    }

    /**
     * Мапира основните данни на продукта
     */
    private function _mapBasicProductData(array &$opencart_product, array $supplier_product_data, array $full_feed_data) {
        // Model - използваме code
        $opencart_product['model'] = isset($supplier_product_data['code']) ? $this->_getXmlNodeValue($supplier_product_data['code']) : '';
        $opencart_product['sku'] = isset($supplier_product_data['code']) ? $this->_getXmlNodeValue($supplier_product_data['code']) : '';
        
        // Part Number
        $opencart_product['mpn'] = isset($supplier_product_data['partnum']) ? $this->_getXmlNodeValue($supplier_product_data['partnum']) : '';

        // Име на продукта
        if (isset($supplier_product_data['name'])) {
            $product_name = $this->_getXmlNodeValue($supplier_product_data['name']);
            $product_name_trimmed = mb_strlen($product_name) > 255 ? mb_substr($product_name, 0, 255) . '...' : $product_name;
            $opencart_product['name'] = $product_name_trimmed;
            if (empty($opencart_product['meta_title'])) {
                 $opencart_product['meta_title'] = $product_name_trimmed;
            }
            unset($product_name, $product_name_trimmed);
        }

        // Описание - използваме searchstring ако няма друго описание
        if (isset($supplier_product_data['general_description'])) {
            $description = $this->_getXmlNodeValue($supplier_product_data['general_description']);
            if (!empty(trim(strip_tags($description)))) {
                $description_trimmed = mb_strlen($description) > 2000 ? mb_substr($description, 0, 2000) . '...' : $description;
                $opencart_product['description'] = $description_trimmed;
                unset($description_trimmed);
            }
            unset($description);
        } elseif (isset($supplier_product_data['searchstring'])) {
            $searchstring = $this->_getXmlNodeValue($supplier_product_data['searchstring']);
            if (!empty(trim(strip_tags($searchstring)))) {
                $opencart_product['description'] = $searchstring;
            }
            unset($searchstring);
        }

        // Количество - спрямо product_status
        $product_status = isset($supplier_product_data['product_status']) ? $this->_getXmlNodeValue($supplier_product_data['product_status']) : '';
        
        // Логика за количество според плана за работа:
        // "наличен" -> 10 бр, "обади се" или "не е наличен" -> не импортираме (qty = 0, status = 0)
        if (mb_strtolower($product_status) === 'наличен') {
            $opencart_product['quantity'] = 10; // Фиксирани 10 броя за наличен продукт
            $opencart_product['status'] = 1;
        } else {
            // За продукти които не са налични - нулираме количеството и деактивираме
            $opencart_product['quantity'] = 0;
            $opencart_product['status'] = 0;
        }

        // Цена - конвертиране от USD в BGN
        if (isset($supplier_product_data['price'])) {
            $price_usd = (float)$this->_getXmlNodeValue($supplier_product_data['price'], 0.00);
            
            // Извличаме курса от rates секцията на фийда
            $bgn_per_usd = 1.67; // По подразбиране
            if (isset($full_feed_data['rates']['BGN-per-USD'])) {
                $bgn_per_usd = (float)$this->_getXmlNodeValue($full_feed_data['rates']['BGN-per-USD'], 1.67);
            }
            
            // Конвертиране USD -> BGN
            $price_bgn = $price_usd * $bgn_per_usd;
            $opencart_product['price'] = round($price_bgn, 2);
        } else {
            $opencart_product['price'] = 0.00;
        }

        $opencart_product['date_available'] = date('Y-m-d');
    }

    /**
     * Мапира изображенията на продукта
     */
    private function _mapProductImages(array &$opencart_product, array $supplier_product_data) {
        // Главно изображение от main_picture_url
        if (isset($supplier_product_data['main_picture_url'])) {
            $main_image_url = $this->_getXmlNodeValue($supplier_product_data['main_picture_url']);
            if (!empty($main_image_url)) {
                $opencart_product['image'] = $main_image_url;
            }
        }

        // Допълнителни изображения от gallery
        if (isset($supplier_product_data['gallery']['picture'])) {
            $gallery_pictures = $supplier_product_data['gallery']['picture'];
            
            // Проверяваме дали е единично изображение или масив
            if (isset($gallery_pictures['picture_url'])) {
                $gallery_pictures = [$gallery_pictures];
            }

            $max_images = 20;
            $images_count = 0;
            $sort_order = 0;

            foreach ($gallery_pictures as $picture) {
                if ($images_count >= $max_images) {
                    break;
                }

                if (isset($picture['picture_url'])) {
                    $picture_url = $this->_getXmlNodeValue($picture['picture_url']);
                    if (!empty($picture_url)) {
                        if (empty($opencart_product['image'])) {
                            $opencart_product['image'] = $picture_url;
                        }
                        $opencart_product['product_image'][] = [
                            'image'      => $picture_url,
                            'sort_order' => $sort_order++
                        ];
                        $images_count++;
                    }
                    unset($picture_url);
                }
            }
            unset($gallery_pictures);
        }
    }

    /**
     * Мапира теглото на продукта
     */
    private function _mapProductWeight(array &$opencart_product, array $supplier_product_data) {
        // Most Computers XML няма отделно поле за тегло в основната структура
        // Може да се извлече от properties ако има
        $opencart_product['weight'] = 0.00;
    }

    /**
     * Мапира статуса на наличност
     */
    private function _mapProductStockStatus(array &$opencart_product, array $supplier_product_data) {
        $opencart_product['stock_status_id'] = $opencart_product['quantity'] > 0 ? $this->config->get('config_stock_status_id') : $this->config->get('config_out_of_stock_status_id');
    }

    /**
     * Мапира категориите на продукта
     */
    private function _mapProductCategories(array &$opencart_product, array $supplier_product_data) {
        // Most Computers използва classname_full за пълния път на категорията
        // Формат: "All » UPS » Line-interactive"
        if (isset($supplier_product_data['classname_full'])) {
            $category_path = $this->_getXmlNodeValue($supplier_product_data['classname_full']);
            
            if (!empty($category_path)) {
                // Премахваме "All » " от началото ако съществува
                $category_path = preg_replace('/^All\s*»\s*/i', '', $category_path);
                
                // Заменяме разделителя "»" с " > " за единност
                $category_path = str_replace('»', '>', $category_path);
                
                $opencart_product['categories_data_source'][] = [
                    'name' => isset($supplier_product_data['classname']) ? $this->_getXmlNodeValue($supplier_product_data['classname']) : '',
                    'branch' => $category_path
                ];
            }
        }
        
        // Добавяме category и subcategory ако има
        if (isset($supplier_product_data['category']['@attributes']['id'])) {
            $category_name = $this->_getXmlNodeValue($supplier_product_data['category']);
            if (!empty($category_name)) {
                $opencart_product['categories_data_source'][] = [
                    'name' => $category_name,
                    'branch' => $category_name
                ];
            }
        }
    }

    /**
     * Мапира производителя на продукта
     */
    private function _mapProductManufacturer(array &$opencart_product, array $supplier_product_data) {
        // Most Computers използва manufacturer поле
        if (isset($supplier_product_data['manufacturer'])) {
            $manufacturer_name = $this->_getXmlNodeValue($supplier_product_data['manufacturer']);
            
            if (!empty($manufacturer_name)) {
                $opencart_product['manufacturer_name_source'] = $manufacturer_name;

                $language_id = (int)$this->config->get('config_language_id') ?: 1;
                $this->_preloadManufacturers($language_id);

                $normalized_manufacturer_name = mb_strtolower($manufacturer_name);

                if (isset($this->manufacturers_cache[$normalized_manufacturer_name])) {
                    $manufacturer = $this->manufacturers_cache[$normalized_manufacturer_name];
                    $opencart_product['manufacturer_id'] = $manufacturer['manufacturer_id'];
                } else {
                    $new_manufacturer_id = $this->_createNewManufacturer($manufacturer_name);
                    if ($new_manufacturer_id) {
                        $opencart_product['manufacturer_id'] = $new_manufacturer_id;
                        $this->manufacturers_cache[$normalized_manufacturer_name] = [
                            'manufacturer_id' => $new_manufacturer_id,
                            'original_name' => $manufacturer_name,
                            'image' => '',
                            'sort_order' => 0
                        ];
                    } else {
                        $opencart_product['manufacturer_name_source'] = $manufacturer_name;
                    }
                }
            }
        }
    }

    /**
     * Мапира атрибутите на продукта
     */
    private function _mapProductAttributes(array &$opencart_product, array $supplier_product_data) {
        // Most Computers използва properties секция за атрибути
        if (isset($supplier_product_data['properties']['property'])) {
            $properties = $supplier_product_data['properties']['property'];
            
            // Проверяваме дали е единичен property или масив
            if (isset($properties['@attributes']['name'])) {
                $properties = [$properties];
            }

            $max_attributes = 50;
            $attributes_count = 0;

            foreach ($properties as $property) {
                if ($attributes_count >= $max_attributes) {
                    break;
                }

                $attr_name = '';
                $attr_value = '';

                // Извличаме name от атрибутите
                if (isset($property['@attributes']['name'])) {
                    $attr_name = $this->_getXmlNodeValue($property['@attributes']['name']);
                }

                // Извличаме стойността на атрибута
                if (is_string($property)) {
                    $attr_value = $this->_getXmlNodeValue($property);
                } elseif (isset($property[0]) && is_string($property[0])) {
                    $attr_value = $this->_getXmlNodeValue($property[0]);
                }

                if (!empty($attr_name) && !empty($attr_value)) {
                    $attr_name_trimmed = mb_strlen($attr_name) > 100 ? mb_substr($attr_name, 0, 100) . '...' : $attr_name;
                    $attr_value_trimmed = mb_strlen($attr_value) > 500 ? mb_substr($attr_value, 0, 500) . '...' : $attr_value;

                    $opencart_product['attributes_data_source'][] = [
                        'name'  => $attr_name_trimmed,
                        'value' => $attr_value_trimmed
                    ];
                    $attributes_count++;
                }

                unset($attr_name, $attr_value, $attr_name_trimmed, $attr_value_trimmed);
            }

            unset($properties);
        }
    }

    /**
     * Предварително зарежда всички марки/производители от OpenCart в кеш
     * @param int $language_id ID на езика
     * @return bool True при успех, false при грешка
     */
    private function _preloadManufacturers($language_id) {
        if ($this->manufacturers_cache_loaded) {
            return true;
        }

        try {
            $query = $this->db->query("
                SELECT m.manufacturer_id, m.name, m.image, m.sort_order
                FROM `" . DB_PREFIX . "manufacturer` m
                ORDER BY m.name ASC
            ");

            foreach ($query->rows as $manufacturer) {
                $normalized_name = mb_strtolower($manufacturer['name']);
                $this->manufacturers_cache[$normalized_name] = [
                    'manufacturer_id' => $manufacturer['manufacturer_id'],
                    'original_name' => $manufacturer['name'],
                    'image' => $manufacturer['image'],
                    'sort_order' => $manufacturer['sort_order']
                ];
            }

            $this->manufacturers_cache_loaded = true;
            $this->writeToCronLog("Most Computers Connector: Заредени " . count($this->manufacturers_cache) . " марки в кеша.", 'mfs_manufacturers.log');
            return true;

        } catch (Exception $e) {
            $this->writeToCronLog("Most Computers Connector: Грешка при зареждане на марки в кеша: " . $e->getMessage(), 'mfs_manufacturers.log');
            return false;
        }
    }

    /**
     * Създава нова марка/производител в OpenCart
     * @param string $manufacturer_name Име на марката
     * @return int|false ID на новата марка или false при грешка
     */
    private function _createNewManufacturer($manufacturer_name) {
        try {
            $this->db->query("
                INSERT INTO `" . DB_PREFIX . "manufacturer`
                SET `name` = '" . $this->db->escape($manufacturer_name) . "',
                    `image` = '',
                    `sort_order` = '0'
            ");

            $manufacturer_id = $this->db->getLastId();

            if ($manufacturer_id) {
                $this->writeToCronLog("Most Computers Connector: Създадена нова марка '{$manufacturer_name}' с ID {$manufacturer_id}", 'mfs_manufacturers.log');
                return $manufacturer_id;
            }

            return false;

        } catch (Exception $e) {
            $this->writeToCronLog("Most Computers Connector: Грешка при създаване на марка '{$manufacturer_name}': " . $e->getMessage(), 'mfs_manufacturers.log');
            return false;
        }
    }

    /**
     * Обработва марката на продукт от атрибутите и връща manufacturer_id
     * @param array $product_data Данни на продукта
     * @return int|null ID на марката или null ако не е намерена
     */
    public function processProductManufacturer(&$product_data) {
        if (isset($product_data['manufacturer_id']) && !empty($product_data['manufacturer_id'])) {
            return $product_data['manufacturer_id'];
        }

        $language_id = (int)$this->config->get('config_language_id') ?: 1;
        $this->_preloadManufacturers($language_id);

        if (!isset($product_data['attributes_data_source']) || empty($product_data['attributes_data_source'])) {
            return null;
        }

        foreach ($product_data['attributes_data_source'] as $attribute) {
            if (empty($attribute['name']) || empty($attribute['value'])) {
                continue;
            }

            $attribute_name = mb_strtolower($attribute['name']);
            if ($attribute_name === 'manufacturer' || $attribute_name === 'марка' || $attribute_name === 'производител') {
                $brand_name = trim($attribute['value']);

                if (empty($brand_name)) {
                    continue;
                }

                $normalized_brand_name = mb_strtolower($brand_name);

                if (isset($this->manufacturers_cache[$normalized_brand_name])) {
                    $manufacturer = $this->manufacturers_cache[$normalized_brand_name];
                    $this->writeToCronLog("Most Computers Connector: Намерена съществуваща марка '{$manufacturer['original_name']}' с ID {$manufacturer['manufacturer_id']}", 'mfs_manufacturers.log');
                    $product_data['manufacturer'] = $manufacturer['original_name'];
                    return $manufacturer['manufacturer_id'];
                } else {
                    $new_manufacturer_id = $this->_createNewManufacturer($brand_name);

                    if ($new_manufacturer_id) {
                        $this->manufacturers_cache[$normalized_brand_name] = [
                            'manufacturer_id' => $new_manufacturer_id,
                            'original_name' => $brand_name,
                            'image' => '',
                            'sort_order' => 0
                        ];

                        $this->writeToCronLog("Most Computers Connector: Създадена и кеширана нова марка '{$brand_name}' с ID {$new_manufacturer_id}", 'mfs_manufacturers.log');
                        return $new_manufacturer_id;
                    }
                }
            }
        }

        return null;
    }

    /**
     * Обработва атрибутите на продукт и връща готови данни за основния модел
     * @param array $product_data Данни на продукта
     * @param int $language_id ID на езика
     * @return array Масив с обработени атрибути готови за основния модел
     */
    public function processProductAttributes($product_data, $language_id = 1) {
        $processed_attributes = [];

        if (!isset($product_data['attributes_data_source']) || empty($product_data['attributes_data_source'])) {
            return $processed_attributes;
        }

        $this->_preloadOpenCartAttributes($language_id);
        $default_attribute_group_id = $this->_getDefaultAttributeGroupId($language_id);

        foreach ($product_data['attributes_data_source'] as $attribute) {
            if (empty($attribute['name']) || empty($attribute['value'])) {
                continue;
            }

            $normalized_name = mb_strtolower($attribute['name']);

            if (isset($this->opencart_attributes_cache[$normalized_name])) {
                $oc_attribute = $this->opencart_attributes_cache[$normalized_name];

                if(isset($processed_attributes[ $oc_attribute['attribute_id'] ])) {
                    continue;
                }

                $processed_attributes[ $oc_attribute['attribute_id'] ] = [
                    'attribute_id' => $oc_attribute['attribute_id'],
                    'name' => $oc_attribute['original_name'],
                    'value' => $attribute['value'],
                    'attribute_group_id' => $oc_attribute['attribute_group_id']
                ];
            } else {
                $new_attribute_id = $this->_createNewAttribute($attribute['name'], $default_attribute_group_id, $language_id);

                if ($new_attribute_id) {
                    $this->opencart_attributes_cache[$normalized_name] = [
                        'attribute_id' => $new_attribute_id,
                        'attribute_group_id' => $default_attribute_group_id,
                        'original_name' => $attribute['name']
                    ];

                    $processed_attributes[ $new_attribute_id ] = [
                        'attribute_id' => $new_attribute_id,
                        'name' => $attribute['name'],
                        'value' => $attribute['value'],
                        'attribute_group_id' => $default_attribute_group_id
                    ];
                }
            }
        }

        return array_values($processed_attributes);
    }

    /**
     * Предварително зарежда всички OpenCart атрибути в кеш
     * @param int $language_id ID на езика
     * @return bool True при успех, false при грешка
     */
    private function _preloadOpenCartAttributes($language_id) {
        if ($this->opencart_attributes_cache_loaded) {
            return true;
        }

        try {
            $query = $this->db->query("
                SELECT a.attribute_id, ad.name, a.attribute_group_id
                FROM `" . DB_PREFIX . "attribute` a
                LEFT JOIN `" . DB_PREFIX . "attribute_description` ad
                    ON (a.attribute_id = ad.attribute_id)
                WHERE ad.language_id = '" . (int)$language_id . "'
            ");

            foreach ($query->rows as $attribute) {
                $normalized_name = mb_strtolower($attribute['name']);
                $this->opencart_attributes_cache[$normalized_name] = [
                    'attribute_id' => $attribute['attribute_id'],
                    'attribute_group_id' => $attribute['attribute_group_id'],
                    'original_name' => $attribute['name']
                ];
            }

            $this->opencart_attributes_cache_loaded = true;
            return true;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Получава ID на атрибутната група по подразбиране
     * @param int $language_id ID на езика
     * @return int ID на атрибутната група
     */
    private function _getDefaultAttributeGroupId($language_id) {
        $query = $this->db->query("
            SELECT ag.attribute_group_id
            FROM `" . DB_PREFIX . "attribute_group` ag
            LEFT JOIN `" . DB_PREFIX . "attribute_group_description` agd
                ON (ag.attribute_group_id = agd.attribute_group_id)
            WHERE agd.language_id = '" . (int)$language_id . "'
                AND (agd.name = 'General' OR agd.name = 'Общи' OR agd.name = 'Default')
            LIMIT 1
        ");

        if ($query->num_rows) {
            return $query->row['attribute_group_id'];
        }

        return $this->_createDefaultAttributeGroup($language_id);
    }

    /**
     * Създава атрибутна група по подразбиране
     * @param int $language_id ID на езика
     * @return int ID на новосъздадената група
     */
    private function _createDefaultAttributeGroup($language_id) {
        $this->db->query("
            INSERT INTO `" . DB_PREFIX . "attribute_group`
            SET `sort_order` = '1'
        ");

        $group_id = $this->db->getLastId();

        if ($group_id) {
            $this->db->query("
                INSERT INTO `" . DB_PREFIX . "attribute_group_description`
                SET `attribute_group_id` = '" . (int)$group_id . "',
                    `language_id` = '" . (int)$language_id . "',
                    `name` = 'Общи'
            ");
        }

        return $group_id;
    }

    /**
     * Създава нов атрибут в OpenCart
     * @param string $attribute_name Име на атрибута
     * @param int $attribute_group_id ID на атрибутната група
     * @param int $language_id ID на езика
     * @return int|false ID на новия атрибут или false при грешка
     */
    private function _createNewAttribute($attribute_name, $attribute_group_id, $language_id) {
        try {
            $this->db->query("
                INSERT INTO `" . DB_PREFIX . "attribute`
                SET `attribute_group_id` = '" . (int)$attribute_group_id . "',
                    `sort_order` = '0'
            ");

            $attribute_id = $this->db->getLastId();

            if ($attribute_id) {
                $this->db->query("
                    INSERT INTO `" . DB_PREFIX . "attribute_description`
                    SET `attribute_id` = '" . (int)$attribute_id . "',
                        `language_id` = '" . (int)$language_id . "',
                        `name` = '" . $this->db->escape($attribute_name) . "'
                ");

                return $attribute_id;
            }

            return false;

        } catch (Exception $e) {
            return false;
        }
    }

    private function writeToCronLog($message, $log_file=null) {
        if(!$this->logging_enabled) return;
        $log_file = $log_file ? DIR_LOGS . $log_file : DIR_LOGS . 'multi_feed_syncer.log'; 
        $time = date('Y-m-d H:i:s');
        file_put_contents($log_file, $time . ': ' . $message . PHP_EOL, FILE_APPEND);
    }

    /**
     * Извлича всички уникални пътища на категории от синхронизиращия файл
     * @return array Масив с уникални пътища на категории
     */
    public function getCategoriesFromFile() {
        $categories = [];

        try {
            $xml_data = $this->requestCachedSyncData();
            if(!$xml_data){
                $xml_data = $this->requestSyncData();
                $this->setCachedSyncData($xml_data);
            }
            if (!$xml_data) {
                return [];
            }

            $language_id = (int)$this->config->get('config_language_id') ?: 1;
            $this->_preloadManufacturers($language_id);

            // Поточно четене на XML за извличане на категории
            $xml_reader = new \XMLReader();
            if (!$xml_reader->XML($xml_data)) {
                return [];
            }

            // Навигиране до productlist елемента
            while ($xml_reader->read() && !($xml_reader->nodeType == \XMLReader::ELEMENT && $xml_reader->name == 'productlist')) {
            }

            if ($xml_reader->nodeType == \XMLReader::ELEMENT && $xml_reader->name == 'productlist') {
                while ($xml_reader->read()) {
                    if ($xml_reader->nodeType == \XMLReader::ELEMENT && $xml_reader->name == 'product') {
                        $node = @$xml_reader->expand();
                        if ($node) {
                            $product_data = $this->_convertDomNodeToArray($node);

                            // Извличаме категорията от classname_full
                            if (isset($product_data['classname_full'])) {
                                $category_path = $this->_getXmlNodeValue($product_data['classname_full']);
                                if (!empty($category_path)) {
                                    $category_path = preg_replace('/^All\s*»\s*/i', '', $category_path);
                                    $category_path = str_replace('»', ' > ', $category_path);
                                    $categories[$category_path] = true;
                                    
                                    $this->_addParentCategories($category_path, $categories);
                                }
                            }
                        }
                    } elseif ($xml_reader->nodeType == \XMLReader::END_ELEMENT && $xml_reader->name == 'productlist') {
                        break;
                    }
                }
            }
            $xml_reader->close();

        } catch (Exception $e) {
            return [];
        }

        $sorted_categories = array_keys($categories);
        sort($sorted_categories);

        return $sorted_categories;
    }

    /**
     * Добавя всички родителски категории към масива с категории
     * @param string $category_branch Пълният път на категорията разделен с " > "
     * @param array &$categories Масив с категории (по референция)
     */
    private function _addParentCategories($category_branch, &$categories) {
        $parts = explode('>', $category_branch);
        $current_path = '';

        foreach ($parts as $part) {
            $part = trim($part);
            if (!empty($part)) {
                if (!empty($current_path)) {
                    $current_path .= ' > ';
                }
                $current_path .= $part;
                $categories[$current_path] = true;
            }
        }
    }

    public function getCachedSyncDataFilePath(){
        return DIR_CACHE . 'mostcomputers_feed_data.xml';
    }

    private function requestCachedSyncData(){
        $cached_data_file = $this->getCachedSyncDataFilePath();
        if(file_exists($cached_data_file)){
            $filemtime = filemtime($cached_data_file);
            if(time() - $filemtime < 86400) {
                $file_size = filesize($cached_data_file);
                $file_size_mb = $file_size / 1024 / 1024;

                if ($file_size_mb > 50) {
                    error_log("Most Computers Connector: Зареждане на голям кеширан файл ({$file_size_mb}MB): {$cached_data_file}");
                }

                if ($file_size_mb > 100) {
                    $handle = fopen($cached_data_file, 'r');
                    if ($handle) {
                        $content = stream_get_contents($handle);
                        fclose($handle);
                        return $content;
                    }
                    return false;
                } else {
                    return file_get_contents($cached_data_file);
                }
            }
        }
        return false;
    }

    private function setCachedSyncData($raw_data){
        $cached_data_file = $this->getCachedSyncDataFilePath();
        file_put_contents($cached_data_file, $raw_data);
        $this->_clearCachedProducts();
    }

    private function _getCachedProductsFilePath() {
        $storage_dir = DIR_STORAGE . 'multi_feed_syncer/' . $this->connector_key . '/';
        return $storage_dir . 'products_data.serialized';
    }

    private function _loadCachedProducts() {
        $cache_file = $this->_getCachedProductsFilePath();

        if (!file_exists($cache_file)) {
            return false;
        }

        try {
            $serialized_data = file_get_contents($cache_file);
            if ($serialized_data === false) {
                return false;
            }

            $products_data = unserialize($serialized_data);
            if ($products_data === false || !is_array($products_data)) {
                unlink($cache_file);
                return false;
            }

            return $products_data;
        } catch (Exception $e) {
            if (file_exists($cache_file)) {
                unlink($cache_file);
            }
            return false;
        }
    }

    private function _saveCachedProducts($products_data) {
        if (!is_array($products_data) || empty($products_data)) {
            return false;
        }

        $cache_file = $this->_getCachedProductsFilePath();
        $dir = dirname($cache_file);

        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }

        try {
            $serialized_data = serialize($products_data);
            return file_put_contents($cache_file, $serialized_data) !== false;
        } catch (Exception $e) {
            return false;
        }
    }

    private function _clearCachedProducts() {
        $cache_file = $this->_getCachedProductsFilePath();

        if (!file_exists($cache_file)) {
            return true;
        }

        try {
            return unlink($cache_file);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Рекурсивно конвертира DOMNode обект в PHP масив с оптимизация за памет.
     * @param DOMNode $node DOMNode за конвертиране.
     * @return array|string Конвертираният масив или стринг, ако е текстов възел.
     */
    private function _convertDomNodeToArray(\DOMNode $node) {
        $output = [];
        switch ($node->nodeType) {
            case XML_CDATA_SECTION_NODE:
            case XML_TEXT_NODE:
                $output = trim($node->textContent);
                break;

            case XML_ELEMENT_NODE:
                $child_nodes_length = $node->childNodes->length;
                for ($i = 0; $i < $child_nodes_length; $i++) {
                    $child = $node->childNodes->item($i);
                    $v = $this->_convertDomNodeToArray($child);
                    if (isset($child->tagName)) {
                        $t = $child->tagName;
                        if (!isset($output[$t])) {
                            $output[$t] = [];
                        }
                        $output[$t][] = $v;
                    } elseif ($v !== '' && $v !== null) {
                         $output[] = $v;
                    }
                    unset($child, $v);
                }

                // Обработка на атрибути
                $attributes_length = $node->attributes->length;
                if ($attributes_length && !empty($output)) {
                    $attributes = [];
                    foreach ($node->attributes as $attrName => $attrNode) {
                        $attributes[$attrName] = (string)$attrNode->value;
                    }
                    if (!is_array($output)) {
                        $output = ['#text' => $output];
                    }
                    $output['@attributes'] = $attributes;
                    unset($attributes);
                } elseif ($attributes_length) {
                     $output = [];
                     foreach ($node->attributes as $attrName => $attrNode) {
                        $output['@attributes'][$attrName] = (string)$attrNode->value;
                    }
                }

                // Опростяване на масиви с един елемент
                if (is_array($output)) {
                    foreach ($output as $t => $v) {
                        if (is_array($v) && count($v) == 1 && $t != '@attributes') {
                            if (array_key_exists(0, $v)) {
                                $output[$t] = $v[0];
                            }
                        }
                    }
                    if (empty($output) && !$attributes_length && !$node->hasChildNodes()) {
                        $output = '';
                    }
                }
                break;
        }
        
        if (is_array($output) && empty($output) && $node->nodeType == XML_ELEMENT_NODE && !$node->attributes->length) {
            $hasRealText = false;
            if ($node->hasChildNodes()) {
                for ($i=0; $i < $node->childNodes->length; $i++) {
                    $childNode = $node->childNodes->item($i);
                    if (($childNode->nodeType == XML_TEXT_NODE || $childNode->nodeType == XML_CDATA_SECTION_NODE) && trim($childNode->textContent) !== '') {
                        $hasRealText = true;
                        break;
                    }
                    if ($childNode->nodeType == XML_ELEMENT_NODE) {
                        $hasRealText = true;
                        break;
                    }
                }
            }
            if (!$hasRealText) {
                return '';
            }
        }

        return $output;
    }

    public function getSyncLogData(array $log_process_data) {
        $info_parts = [];
        if (isset($log_process_data['connection_established'])) {
            $info_parts[] = "Връзка установена: " . ($log_process_data['connection_established'] ? 'Да' : 'Не');
        }
        if (isset($log_process_data['added'])) {
            $info_parts[] = "Добавени: " . (int)$log_process_data['added'];
        }
        if (isset($log_process_data['updated'])) {
            $info_parts[] = "Актуализирани: " . (int)$log_process_data['updated'];
        }
        if (isset($log_process_data['skipped'])) {
            $info_parts[] = "Пропуснати: " . (int)$log_process_data['skipped'];
        }
        if (isset($log_process_data['errors'])) {
            $info_parts[] = "Грешки: " . (int)$log_process_data['errors'];
        }
        if (!empty($log_process_data['message'])) {
            $info_parts[] = "Съобщение: " . htmlspecialchars($log_process_data['message']);
        }
         if (!empty($log_process_data['error_message'])) {
            $info_parts[] = "Съобщение за грешка: " . htmlspecialchars($log_process_data['error_message']);
        }
        return implode(', <br/>' . PHP_EOL, $info_parts);
    }

    private $current_log = '';
    private $mfs_model_instance;

    public function processSupplierFeed($c_key, $mfsc_id, $testmode = false, $mfs_model_instance) {
        $memory_start = memory_get_usage(true);
        $memory_limit = ini_get('memory_limit');
        $memory_limit_bytes = $this->_parseMemoryLimit($memory_limit);

        if(is_object($mfs_model_instance)) {
            $this->mfs_model_instance = $mfs_model_instance;
        }
        else {
            $this->mfs_model_instance = $this->load->model('extension/module/multi_feed_syncer');
            $this->mfs_model_instance->setTestMode($testmode);
        }

        $log_entry_prefix = "Конектор ({$c_key}): ";
        $this->_initializeProductTracking($mfsc_id, $current_log, $log_entry_prefix);

        $current_log = &$this->current_log;
        $sync_stats = [
            'added' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0,
            'message' => '',
            'error_message' => '',
            'connection_established' => false
        ];
        $opencart_products_batch = [];

        $memory_start_mb = $memory_start / 1024 / 1024;
        $memory_limit_mb = $memory_limit_bytes / 1024 / 1024;
        $current_log .= $log_entry_prefix . "Стартова памет: " . round($memory_start_mb, 2) . "MB, Лимит: " . round($memory_limit_mb, 2) . "MB\n";

        $opencart_products_batch = $this->_loadCachedProducts();

        if ($opencart_products_batch !== false) {
            $current_log .= $log_entry_prefix . "Заредени " . count($opencart_products_batch) . " продукта от кеша. Пропускаме извличането на XML данни.\n";
            $sync_stats['connection_established'] = true;
        } else {
            $current_log .= $log_entry_prefix . "Няма кеширани обработени продукти, започваме извличане и обработка на XML данните.\n";

            list($supplier_data_source, $data_is_xml, $connection_ok, $conversion_error) = $this->_requestAndPrepareData($c_key, $current_log, $sync_stats, $log_entry_prefix);
            $sync_stats['connection_established'] = $connection_ok;

            if (!$connection_ok || $conversion_error) {
                return $sync_stats;
            }

            if (empty($supplier_data_source) && !$conversion_error) {
                $current_log .= $log_entry_prefix . "Предупреждение: Източникът на данни е празен за {$c_key}.\n";
                $sync_stats['error_message'] = "Източникът на данни е празен";
                if (empty($sync_stats['message'])) {
                     $sync_stats['message'] = 'Източникът на данни е празен.';
                }
                return $sync_stats;
            }

            if ($data_is_xml && is_string($supplier_data_source)) {
                $opencart_products_batch = $this->_processXmlFeedData($supplier_data_source, $c_key, $current_log, $sync_stats, $log_entry_prefix);

                if (!empty($opencart_products_batch)) {
                    $this->_saveCachedProducts($opencart_products_batch);
                    $current_log .= $log_entry_prefix . "Обработените продукти са записани в кеша за бъдещо използване.\n";
                }
            } else {
                if (empty($sync_stats['error_message'])) {
                    $current_log .= $log_entry_prefix . "Грешка: Неочакван тип на данните за {$c_key}.\n";
                    $sync_stats['error_message'] = "Неочакван тип на данните.";
                }
                return $sync_stats;
            }
        }

        $current_log .= $log_entry_prefix . count($opencart_products_batch) . " продукта са мапирани за {$c_key}.\n";

        if (empty($opencart_products_batch)) {
            if (empty($sync_stats['error_message']) && empty($sync_stats['message'])) {
                $current_log .= $log_entry_prefix . "Няма продукти за обработка от фийда за {$c_key} след мапиране.\n";
                $sync_stats['message'] = 'Няма продукти във фийда за обработка';
            }
            return $sync_stats;
        }

        $sync_result_stats = $this->_syncProducts($opencart_products_batch, $mfsc_id, $c_key, $current_log, $log_entry_prefix);

        $sync_stats['added']   += (isset($sync_result_stats['added']) ? (int)$sync_result_stats['added'] : 0);
        $sync_stats['updated'] += (isset($sync_result_stats['updated']) ? (int)$sync_result_stats['updated'] : 0);
        $sync_stats['skipped'] += (isset($sync_result_stats['skipped']) ? (int)$sync_result_stats['skipped'] : 0);
        $sync_stats['errors']  += (isset($sync_result_stats['errors']) ? (int)$sync_result_stats['errors'] : 0);

        if (!empty($sync_result_stats['message'])) {
            $sync_stats['message'] = trim($sync_stats['message'] . ($sync_stats['message'] ? '; ' : '') . $sync_result_stats['message']);
        }

        if (!empty($sync_result_stats['error_message'])) {
            if (!empty($sync_stats['error_message'])) {
                $sync_stats['error_message'] .= '; ' . $sync_result_stats['error_message'];
            } else {
                $sync_stats['error_message'] = $sync_result_stats['error_message'];
            }
        }

        $memory_end = memory_get_usage(true);
        $memory_peak = memory_get_peak_usage(true);
        $memory_used_mb = ($memory_end - $memory_start) / 1024 / 1024;
        $memory_peak_mb = $memory_peak / 1024 / 1024;

        $current_log .= $log_entry_prefix . "Финален отчет за памет - Използвана: " . round($memory_used_mb, 2) . "MB, Пик: " . round($memory_peak_mb, 2) . "MB\n";

        if ($memory_peak_mb > ($memory_limit_mb * 0.8)) {
            $current_log .= $log_entry_prefix . "ПРЕДУПРЕЖДЕНИЕ: Използваната памет достигна " . round(($memory_peak_mb / $memory_limit_mb) * 100, 1) . "% от лимита!\n";
        }

        $this->_finalizeProductTracking($mfsc_id, $current_log, $log_entry_prefix);
        $this->_resetProductTracking();

        return $sync_stats;
    }

    private function _parseMemoryLimit($memory_limit) {
        if ($memory_limit == '-1') {
            return PHP_INT_MAX;
        }

        $memory_limit = trim($memory_limit);
        $last_char = strtolower($memory_limit[strlen($memory_limit) - 1]);
        $value = (int)$memory_limit;

        switch ($last_char) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    protected function addToLog($message){
        $this->current_log .= print_r($message, true) . PHP_EOL;
    }

    public function getCurrentLog(){
        return $this->current_log;
    }

    private function _requestAndPrepareData($c_key, &$current_log, &$sync_stats, $log_entry_prefix) {
        $raw_data = $this->requestCachedSyncData();
        if(!$raw_data){
            $raw_data = $this->requestSyncData();
            $this->setCachedSyncData($raw_data);
        }
        $connection_ok = false;
        $conversion_error = false;
        $supplier_data_source = null;
        $data_is_xml = false;

        if (!$raw_data) {
            $current_log .= $log_entry_prefix . "Грешка: Неуспешно извличане на данни за {$c_key}.\n";
            $sync_stats['error_message'] = "Неуспешно извличане на данни";
            return [$supplier_data_source, $data_is_xml, $connection_ok, $conversion_error];
        }

        $connection_ok = true;
        $current_log .= $log_entry_prefix . "Данните са извлечени успешно за {$c_key}.\n";

        $connection_type_string = $this->getConnectionType();
        if (is_string($connection_type_string) && stripos($connection_type_string, 'XML') !== false) {
            $data_is_xml = true;
        }

        if ($data_is_xml) {
            $supplier_data_source = $raw_data;
            $current_log .= $log_entry_prefix . "XML данните са получени и ще бъдат обработени поточно за {$c_key}.\n";
        } else {
            $current_log .= $log_entry_prefix . "Предупреждение: Данните за {$c_key} не са XML. Има проблем с обработката.\n";
            $sync_stats['error_message'] = "Неподдържан формат на данните.";
            $supplier_data_source = $raw_data;
        }
        return [$supplier_data_source, $data_is_xml, $connection_ok, $conversion_error];
    }

    private function _processXmlFeedData(string $supplier_data_source, string $c_key, &$current_log, &$sync_stats, string $log_entry_prefix) {
        $opencart_products_batch = [];

        if (empty($supplier_data_source)) {
            $current_log .= $log_entry_prefix . "Грешка: XML източникът на данни е празен за {$c_key}.\n";
            $sync_stats['error_message'] = "XML източникът на данни е празен";
            return $opencart_products_batch;
        }

        $language_id = (int)$this->config->get('config_language_id') ?: 1;
        if ($this->_preloadManufacturers($language_id)) {
            $current_log .= $log_entry_prefix . "Заредени " . count($this->manufacturers_cache) . " марки в кеша.\n";
        }

        // Извличаме rates от XML за конвертиране на валута
        $full_feed_data = [];
        $xml_reader_rates = new \XMLReader();
        if ($xml_reader_rates->XML($supplier_data_source)) {
            while ($xml_reader_rates->read()) {
                if ($xml_reader_rates->nodeType == \XMLReader::ELEMENT && $xml_reader_rates->name == 'rates') {
                    $rates_node_xml = $xml_reader_rates->readOuterXML();
                    if ($rates_node_xml) {
                        $rates_data = $this->convertXMLdataToArray($rates_node_xml);
                        if ($rates_data && is_array($rates_data)){
                            $full_feed_data['rates'] = $rates_data;
                        }
                    }
                    break;
                }
            }
            $xml_reader_rates->close();
        }

        // Поточно четене и обработка на продуктите
        $xml_reader_products = new \XMLReader();
        if (!$xml_reader_products->XML($supplier_data_source)) {
            $current_log .= $log_entry_prefix . "Грешка: Неуспешно зареждане на XML за обработка на продукти за {$c_key}.\n";
            $sync_stats['error_message'] = "Неуспешно зареждане на XML за продукти";
            return $opencart_products_batch;
        }

        // Навигиране до productlist
        while ($xml_reader_products->read() && !($xml_reader_products->nodeType == \XMLReader::ELEMENT && $xml_reader_products->name == 'productlist')) {
        }

        if ($xml_reader_products->nodeType == \XMLReader::ELEMENT && $xml_reader_products->name == 'productlist') {
            $products_processed = 0;
            $memory_start = memory_get_usage(true);

            while ($xml_reader_products->read()) {
                if ($xml_reader_products->nodeType == \XMLReader::ELEMENT && $xml_reader_products->name == 'product') {
                    $node = @$xml_reader_products->expand();
                    if ($node) {
                        $supplier_product_data = $this->_convertDomNodeToArray($node);
                        if ($supplier_product_data && is_array($supplier_product_data)) {
                            $opencart_product_data = $this->mapDataToOpencartProduct($supplier_product_data, $full_feed_data);
                            if ($opencart_product_data) {
                                $opencart_products_batch[] = $opencart_product_data;
                            } else {
                                $sync_stats['skipped']++;
                            }
                        } else {
                            $sync_stats['errors']++;
                        }

                        unset($supplier_product_data, $opencart_product_data, $node);
                        $products_processed++;

                        if ($products_processed % 5 === 0) {
                            if (function_exists('gc_collect_cycles')) {
                                gc_collect_cycles();
                            }
                        }
                    }
                } elseif ($xml_reader_products->nodeType == \XMLReader::END_ELEMENT && $xml_reader_products->name == 'productlist') {
                    break;
                }
            }

            $current_log .= $log_entry_prefix . "Обработени общо {$products_processed} продукта от XML фийда.\n";
        }
        $xml_reader_products->close();

        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }

        return $opencart_products_batch;
    }

    private function _syncProducts($opencart_products_batch, $mfsc_id, $c_key, &$current_log, $log_entry_prefix) {
        if ($this->mfs_model_instance) {
             if (!is_object($this->mfs_model_instance)) {
                 $current_log .= $log_entry_prefix . "КРИТИЧНА ГРЕШКА: Основният модел не успя да се зареди или не е обект в _syncProducts.\n";
                 return ['added' => 0, 'updated' => 0, 'skipped' => 0, 'errors' => count($opencart_products_batch), 'error_message' => 'Основният модел не можа да бъде зареден.'];
             } elseif (!is_callable([$this->mfs_model_instance, 'doSync'])) {
                 $current_log .= $log_entry_prefix . "КРИТИЧНА ГРЕШКА: Методът 'doSync' не съществува.\n";
                 return ['added' => 0, 'updated' => 0, 'skipped' => 0, 'errors' => count($opencart_products_batch), 'error_message' => "Методът 'doSync' не е намерен."];
             }
        }

        $sync_stats = $this->mfs_model_instance->doSync($opencart_products_batch, $mfsc_id);
        $current_log .= $log_entry_prefix . "doSync е завършен за {$c_key}. Статистика: " . print_r($sync_stats, true) . "\n";
        return $sync_stats;
    }

    private function _initializeProductTracking($mfsc_id, &$current_log, $log_entry_prefix) {
        self::$current_sync_product_ids = [];
        self::$product_tracking_enabled = true;
        self::$active_instance = $this;
        $current_log .= $log_entry_prefix . "Инициализирано проследяване на продукти за Most Computers конектор (ID: {$mfsc_id}).\n";
    }

    private function _finalizeProductTracking($mfsc_id, &$current_log, $log_entry_prefix) {
        if (!self::$product_tracking_enabled) {
            return;
        }

        try {
            $current_synced_count = count(self::$current_sync_product_ids);
            $current_log .= $log_entry_prefix . "Проследени {$current_synced_count} продукта в синхронизацията.\n";
        } catch (Exception $e) {
            $error_message = "ГРЕШКА при финализиране на проследяването: " . $e->getMessage();
            $current_log .= $log_entry_prefix . $error_message . "\n";
        }
    }

    private function _resetProductTracking() {
        self::$current_sync_product_ids = [];
        self::$product_tracking_enabled = false;
        self::$active_instance = null;
    }

    public function _trackSyncedProduct($product_id) {
        if (self::$product_tracking_enabled && !in_array($product_id, self::$current_sync_product_ids)) {
            self::$current_sync_product_ids[] = $product_id;
        }
    }

    public static function trackSyncedProductStatic($product_id) {
        if (self::$active_instance && self::$product_tracking_enabled) {
            self::$active_instance->_trackSyncedProduct($product_id);
            return true;
        }
        return false;
    }
}
?>
