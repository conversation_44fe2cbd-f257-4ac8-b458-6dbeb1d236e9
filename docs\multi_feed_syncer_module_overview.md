# MultiFeed Syncer – Подробно описание на функционалностите

## 1. Общ изглед и цел на модула
`MultiFeed Syncer` е административен модул за OpenCart, който управлява синхронизацията на продуктови данни от множество външни доставчици към каталога на магазина. Модулът е изграден с архитектура „основен модел + конектори“, където:
- основният модел `ModelExtensionModuleMultiFeedSyncer` централизира логиката за проверка, подготовка, пакетна обработка и запис на продуктите;
- всеки доставчик има самостоятелен конектор (например `eoffice`, `mostcomputers`), който се грижи за извличането, парсирането и нормализирането на входните данни, преди да ги предаде към основния модел.

Основните функционални направления на модула са:
- управление на регистъра с активни конектори и cron настройки;
- синхронизация на продукти (batch insert/update) с проследяване и статистики;
- обработка на изображения чрез опашка с отложено изтегляне;
- управление на категории, SEO, атрибути и производители;
- централизирана логика за разрешаване на конфликти при дублиращи се SKU от различни доставчици.

## 2. Управление от контролера
Административният контролер `ControllerExtensionModuleMultiFeedSyncer` (`admin/controller/extension/module/multi_feed_syncer.php`) предоставя UI за операторите и е отговорен за следните задачи:
- Зарежда езиковите ресурси, оформя табове („Доставчици“, „Логове“, „Настройки“) и списъци с активни/нови конектори. [`admin/controller/extension/module/multi_feed_syncer.php:16-198`]
- За всеки активен конектор инстанцира модела му и извлича помощни данни: тип на връзката (`getConnectionType()`), последно изтеглен файл (`getLastFileDownloadInfo()`), статус на проверката (`checkConnection()`). [`admin/controller/extension/module/multi_feed_syncer.php:144-198`]
- Разширява функционалността за повторно изтегляне на изходните файлове чрез `downloadFileAgain()` (извиква се през AJAX). [`admin/controller/extension/module/multi_feed_syncer.php:615-647`]
- Поддържа таблица с логове чрез модела (`getLogs()`), дава възможност за преглед и филтриране на резултатите. [`admin/controller/extension/module/multi_feed_syncer.php:471-575`]
- При ръчно стартиране на синхронизация (бутон „Синхронизирай“):
  1. Зарежда конектора по ключ.
  2. Проверява дали конекторът имплементира задължителния метод `processSupplierFeed()`.
  3. Подготовя лог контекст (`$start_time`, `$log_entry_prefix`) и изпраща управлението към конектора, подавайки `mfsc_id`, ключ на конектора и инстанция на основния модел. [`admin/controller/extension/module/multi_feed_syncer.php:331-347`] 
- Отговаря за различни помощни действия: зареждане/запазване на мапинги между категории, управление на OCFilter настройки, тестов режим, генериране на cron инструкции и др. [`admin/controller/extension/module/multi_feed_syncer.php:592-1041`]

## 3. Основен модел: `ModelExtensionModuleMultiFeedSyncer`
Файл: `admin/model/extension/module/multi_feed_syncer.php`

### 3.1. Инсталация и базови таблици
- Създава таблици за логове, регистър на конектори, опашка за изображения, мапинги на категории и релации продукт–конектор. [`multi_feed_syncer.php:76-151`]
- Предоставя методи за добавяне/изтриване/извличане на конектори и логове. [`multi_feed_syncer.php:163-236`]

### 3.2. Основни константи и кешове
- Дефинира размери на batch порции за вмъкване/обновяване и интервал между порциите (`MFS_BATCH_INSERT_CHUNK_SIZE`, `MFS_BATCH_UPDATE_CHUNK_SIZE`, `MFS_SLEEP_BETWEEN_CHUNKS`). [`multi_feed_syncer.php:4-24`]
- Поддържа глобални кешове за категории, атрибути, продукти без категории, SEO ключове и други оптимизационни структури. [`multi_feed_syncer.php:26-356`]

### 3.3. Метод `doSync()` – централна точка
`doSync($opencart_product_data, $mfsc_id)` е основният метод, който приема нормализирани от конектора продукти и ги записва в каталога. Основни стъпки: [`multi_feed_syncer.php:358-664`]
1. **Подготовка:**
   - Зареждане на активните езици (`model_localisation_language->getLanguages()`), настройка на кешове за SEO и категории.
   - Настройка на идентификатора (`sku`) и нулева надценка (`markup_percentage = 0`).
   - Маркиране на текущия конектор (`$current_connector_key`) и запомняне на езика по подразбиране.

2. **Валидиране на входните данни:**
   - Пропускане на продукти без `sku` и логиране на случаите.
   - Обогатяване на данните за всеки продукт: `default_language_name`, `mfsc_id`, `connector_key`.

3. **Намиране на съществуващи продукти:**
   - Порционно извличане от `oc_product` чрез `sku`.
   - Към всяка намерена съвпадаща позиция се събират `product_id`, текуща цена, описание и всички свързани доставчици (таблица `multi_feed_syncer_product_to_connector`). [`multi_feed_syncer.php:507-565`]

4. **Разделяне на продукти за добавяне/обновяване:**
   - Ако `sku` съществува, продуктът се подготвя за обновяване и минава през конфликтната логика.
   - Ако `sku` липсва, продуктът отива в масива за добавяне.

5. **Конфликтна логика за доставчици:**
   - `_resolveSupplierConflict()` избира най-ниската цена и предпочитано описание (приоритет `eoffice`).
   - При запазена цена/описание се избягват излишни обновявания чрез флагове `_apply_price_update` и `_apply_description_update`.
   - Определя се `primary_supplier_mfsc_id` според `self::$connector_priority` и се записва текущият доставчик в `multi_feed_syncer_product_to_connector`. [`multi_feed_syncer.php:955-1046`]

6. **Обработка на групите:**
   - `_processProductsToInsert()` → `_batchInsertProducts()` (пакетно вмъкване в `oc_product`, `product_description`, `product_to_store`, `product_to_category`, SEO URL, атрибути, изображения). [`multi_feed_syncer.php:812-3354`]
   - `_processProductsToUpdate()` → `_batchUpdateProducts()` (пакетно обновяване на цена/количество чрез CASE WHEN, обновяване на категории, SEO URL, атрибути). [`multi_feed_syncer.php:864-3546`]
   - Резултатите (броя добавени/обновени продукти, грешки) се акумулират в `$processed_info`.

7. **Обновяване на описанията:**
   - След `_batchUpdateProducts()` се извиква `_updateProductDescriptionsIfNeeded()` за продукти, при които `_apply_description_update = true`. Методът подготвя порции с `INSERT ... ON DUPLICATE KEY UPDATE` за всички активни езици. [`multi_feed_syncer.php:1096-1142`]

8. **Финализиране:**
   - Логиране на времето, запис на статистиката чрез `addSynchronizationLog()`, и връщане на резултата към конектора/контролера.

### 3.4. Подсистеми в основния модел
- **Кешове и предварително зареждане:** `_loadCategoriesCache()`, `_preloadCategoriesMapping()`, `_preloadProductsWithoutCategories()` и други оптимизационни методи намаляват броя SQL заявки при големи синхронизации. [`multi_feed_syncer.php:3680-4195`]
- **Атрибути:** `processBatchStandardProductAttributes()` комбинира атрибутни данни от конекторите и ги свързва с OC атрибути. [`multi_feed_syncer.php:3199-3335`] (детайлна имплементация в други части на файла и допълнителни doc файлове)
- **Изображения:** `_queueImageForDownload()` и `processBatchProductImages()` изпращат изображенията към асинхронна опашка `product_image_download_queue`, която по-късно се обработва от cron процес. [`multi_feed_syncer.php:3550-3588`, `multi_feed_syncer.php:2471-2550`]
- **Prosledyavane na produkti:** `_trackSyncedProductInConnector()` работи съвместно с конекторите, за да поддържа `multi_feed_syncer_product_to_connector` и специфичните за конектора механизми (напр. eOffice логовете). [`multi_feed_syncer.php:1089-1142`] 

## 4. Конектори
Всеки конектор е разположен в `admin/model/extension/module/multi_feed_syncer_connectors/` и имплементира общ набор от методи:
- `getInstallData()` – връща име, ключ и настройки за конектора. [`mostcomputers.php:37-46`, `eoffice.php:94-126`]
- `getConnectionType()` – HTML описание за UI (линкове, инструкции, бутони за копиране). [`mostcomputers.php:50-119`, `eoffice.php:140-223`]
- `checkConnection()` – проверява достъпността на източника (обикновено HTTP HEAD/GET). [`mostcomputers.php:133-147`, `eoffice.php:314-359`]
- `requestSyncData()` / `requestCachedSyncData()` / `setCachedSyncData()` – извличат и кешират суровите данни (XML/JSON). [`mostcomputers.php:149-205`, `eoffice.php:361-585`]
- `processSupplierFeed($connector_key, $mfsc_id, $testmode, $mfs_model_instance)` – основният метод за синхронизация: инициализира проследяване, изтегля данни, преобразува ги и извиква `doSync()` на основния модел. [`mostcomputers.php:1210-1325`, `eoffice.php:1417-1686`]
- Помощни методи: статистики, логиране, повторно изтегляне на файл, преглед на кешираните данни, обработка на категории/атрибути, и др.

### 4.1. Конектор `eoffice`
Файл: `admin/model/extension/module/multi_feed_syncer_connectors/eoffice.php`

**Ключови особености:**
- Поддържа сложна логика за атрибути и категории, включително мапване чрез допълнителни таблици и филтри за OCFilter. [`eoffice.php:700-2013`]
- Разделя входния фийд на порции и следи използваната памет. [`eoffice.php:1483-1682`]
- Има обширен механизъм за проследяване на продукти:
  - `_initializeProductTracking()` и `_finalizeProductTracking()` управляват статичните масиви и сравняват текущия списък с предходни синхронизации за да открият „изчезнали“ продукти.
  - `_zeroQuantityForProducts()` и `_removeProductsFromTracking()` зануляват количествата и изчистват записа за продукти, които вече не се предлагат. [`eoffice.php:2300-2489`]
  - Статичният метод `trackSyncedProductStatic()` се използва от основния модел за добавяне на продукти към текущата сесия без нужда от нови инстанции на конектора. [`eoffice.php:2501-2516`]
- Предоставя метод `getCategoriesFromFile()` за предварителен анализ на категориите във входния файл, което подпомага UI модулът за създаване на мапинги. [`eoffice.php:2000-2188`]

**Процес на синхронизация:**
1. Зарежда или изтегля XML файла (с възможност за кеширане).
2. Парсира данните (с комбинация от `SimpleXML`, `XMLReader` и конвертиране към масив). [`eoffice.php:586-695`]
3. За всеки продукт изгражда структура на OpenCart продукт (`mapDataToOpencartProduct()`), включително описание, изображения, категории, атрибути, производител, характеристики. [`eoffice.php:748-1961`]
4. Предава нормализираните продукти на основния модел чрез `doSync()`.
5. След приключване логовете се допълват със статистика (брой добавени, обновени продукти, памет, време). [`eoffice.php:1612-1679`]

### 4.2. Конектор `mostcomputers`
Файл: `admin/model/extension/module/multi_feed_syncer_connectors/mostcomputers.php`

**Ключови особености:**
- Подобен интерфейс на eOffice, но адаптиран към XML структурата на Most Computers.
- Мапингът се разделя на помощни методи (`_mapBasicProductData`, `_mapProductImages`, `_mapProductWeight`, `_mapProductStockStatus`, `_mapProductCategories`, `_mapProductManufacturer`, `_mapProductAttributes`). [`mostcomputers.php:191-688`]
- Има механизъм за конвертиране на валута USD → BGN с използване на курс, извлечен от XML. [`mostcomputers.php:320-337`]
- Пропуска продукти със статус „обади се“ / „не е наличен“, задава количество 10 при наличност и обработва основните изображения. [`mostcomputers.php:339-563`]
- Присвоява категории на база „CategoryBranch“ от XML, изпраща атрибутите и поддържа логове аналогично на eOffice (но с по-лека логика за проследяване). [`mostcomputers.php:545-937`]
- Поддържа кеширането на изтеглен XML файл за повторно използване (`getLastFileDownloadInfo()`, `downloadFileAgain()`). [`mostcomputers.php:939-1024`]

**Процес на синхронизация:**
1. Изтегля XML (или използва кеша), парсира го в масив.
2. Преобразува всеки запис в унифицирана структура на продукт.
3. Групира продуктите и ги подава на основния модел чрез `doSync()` (подобна структура на `processSupplierFeed()` като при eOffice). [`mostcomputers.php:1210-1497`]
4. Използва `_initializeProductTracking()` / `_finalizeProductTracking()` за базово проследяване, без сложната логика за изчезнали продукти.

## 5. Конфликтна логика за доставчици
Фокусирана в `_resolveSupplierConflict()` на основния модел.
- Определя се дали текущият продукт е предоставен от други конектори (чрез `connector_keys`).
- Цената се обновява само ако новата е по-ниска; в противен случай се запазва старото значение и `_apply_price_update` се занулява. [`multi_feed_syncer.php:969-1023`]
- Описанието се обновява, ако:
  - доставчикът е предпочитан (`eoffice`), или
  - досега не е имало предпочитан доставчик и базата няма описание.
- Комбинация от флагове `_apply_price_update` и `_apply_description_update` гарантира, че при запис в базата се правят само нужните UPDATE заявки.
- Пази се линк към предпочитания доставчик в `primary_supplier_mfsc_id` и се записва асоциацията продукт–конектор.

## 6. Обработка на изображения
- Конекторите генерират списък с URL адреси (главно изображение + допълнителни) и ги предават към основния модел.
- Методът `processBatchProductImages()` поставя записите в `product_image_download_queue` с информация дали изображението е главно и какъв е `sort_order`. [`multi_feed_syncer.php:2471-2550`, `multi_feed_syncer.php:2570-2664`]
- Допълнителен cron скрипт (извън обсега на този документ) изтегля физически файловете и ги свързва с продукта.

## 7. Управление на категории
- Конекторите изпращат списъци с category paths (често като `Категория1|Категория2`).
- Основният модел преобразува пътищата в `Категория1 > Категория2` и използва кеша `self::$categories_cache`, за да намери `category_id`.
- Ако продуктът няма категория, `_assignCategoriesToProductOptimized()` използва предварително заредени mappings (`multi_feed_syncer_categories_mapping`) за автоматично присвояване и включва всички родителски категории. [`multi_feed_syncer.php:3780-3859`]
- Поддържат се помощни методи за презареждане/изчистване на кеша и декодиране на HTML entities.

## 8. Атрибути и производители
- Конекторите мапват производителите към вътрешните ID чрез помощни методи (`_mapProductManufacturer()`). При нужда се създават нови записи в `manufacturer` таблицата. [`eoffice.php:1051-1267`, `mostcomputers.php:565-688`]
- Атрибутите се агрегирани в масив (`attributes_data_source`) и се обработват от `processBatchStandardProductAttributes()` на основния модел. Това включва откриване/създаване на атрибути, групи и свързване към продуктите. [`multi_feed_syncer.php:3219-3335`]
- Модулът поддържа и логика за OCFilter, описана подробно в `docs/ocfilter_integration_*`. Конекторите могат да подготвят OCFilter атрибути, които основният модел записва в съответните таблици.

## 9. Логове и мониторинг
- Всеки конектор поддържа собствен лог стринг (`$current_log`), който се добавя към cron лога след всяка операция. [`mostcomputers.php:1207-1325`, `eoffice.php:1414-1686`]
- Основният модел записва подробни логове в:
  - `system/storage/logs/multi_feed_syncer.log` – общи събития;
  - `mfs_products.log`, `mfs_products_add.log`, `mfs_products_update.log` – резултати от insert/update операции;
  - `mfs_product_tracking.log` – проследяване на продуктите и конфликтната логика;
  - `mfs_categories.log`, `mfs_attributes.log`, `mfs_images.log` – специфични подсистеми.
- Таблицата `multi_feed_syncer_logs` съхранява обобщена информация за всяка синхронизация (статус, продължителност, описание). [`multi_feed_syncer.php:76-98`, `admin/controller/...:471-575`]

## 10. Cron и автоматизация
- Всеки конектор предоставя команда за cron чрез `getCronInfo()`. Основният cron скрипт поема параметъра (ключа на конектора) и извиква процеса за синхронизация без UI. [`mostcomputers.php:123-130`, `eoffice.php:260-313`]
- Има отделни cron задачи за обработка на `product_image_download_queue`, зануляване на количества и други периодични операции (скриптовете са в `system/storage/cron/`).

## 11. Разширения и документи
Проектът съдържа допълнителни документации в `docs/`:
- `product_tracking_system.md` – задълбочено описание на таблицата `multi_feed_syncer_product_to_connector` и процесите за проследяване на продуките. [`docs/product_tracking_system.md`]
- `categories_mapping_functionality.md`, `category_assignment_implementation.md` – описват мапинга и присвояването на категории.
- `ocfilter_*` – ръководства за интеграция с OCFilter.
- `standard_attributes_optimization.md`, `attributes_validation_and_sql_formatting.md` – конкретни оптимизации за атрибутни обработки.

## 12. Поток на данните – обобщение
1. **UI действие:** операторът стартира синхронизация от контролера или cron задачата.
2. **Конектор:** `processSupplierFeed()` изтегля/кешира източника, парсира го, нормализира данните и подготвя масив от продукти.
3. **Основен модел:** `doSync()` разделя продуктите на нови и съществуващи, разрешава конфликти, извършва batch insert/update, генерира SEO, обработва категории, атрибути и изображения.
4. **Проследяване:** продуктът се маркира в `multi_feed_syncer_product_to_connector`, логовете се обновяват, отложените задачи за изображения се записват в опашката.
5. **Резултат:** контролерът показва статистика (добавени/обновени/пропуснати) и записва информация за оператора.

---
Този документ описва детайлно основните класове, методи и потоци в модула MultiFeed Syncer и конекторите `eoffice` и `mostcomputers`. При нужда от още по-специфични детайли, препоръчително е да се консултират приложенията в `docs/`, логовете и самите кодови файлове.
